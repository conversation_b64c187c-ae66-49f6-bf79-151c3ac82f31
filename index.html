<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三角洲行动随机选择器</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>三角洲行动随机选择器</h1>
            <p>选择你想要的地图、干员、装备和枪械，然后开始随机抽取！</p>
        </header>

        <main>
            <!-- 地图选择模块 -->
            <section class="selection-module" id="map-module">
                <div class="module-header">
                    <h2>地图选择</h2>
                    <div class="select-buttons">
                        <button class="select-all-btn" id="select-all-maps">全选</button>
                        <button class="deselect-all-btn" id="deselect-all-maps">取消全选</button>
                    </div>
                </div>
                <div class="selection-grid" id="map-grid">
                    <!-- 地图选项将通过JavaScript动态生成 -->
                </div>
            </section>

            <!-- 干员选择模块 -->
            <section class="selection-module" id="operator-module">
                <div class="module-header">
                    <h2>干员选择</h2>
                    <div class="select-buttons">
                        <button class="select-all-btn" id="select-all-operators">全选</button>
                        <button class="deselect-all-btn" id="deselect-all-operators">取消全选</button>
                    </div>
                </div>
                <div class="selection-grid" id="operator-grid">
                    <!-- 干员选项将通过JavaScript动态生成 -->
                </div>
            </section>

            <!-- 装备选择模块 -->
            <section class="selection-module" id="equipment-module">
                <h2>装备选择</h2>
                <div class="equipment-categories">
                    <div class="equipment-category">
                        <div class="category-header">
                            <h3>头盔</h3>
                            <div class="select-buttons">
                                <button class="select-all-btn" id="select-all-helmets">全选</button>
                                <button class="deselect-all-btn" id="deselect-all-helmets">取消全选</button>
                            </div>
                        </div>
                        <div class="equipment-grid" id="helmet-grid">
                            <!-- 头盔选项将通过JavaScript动态生成 -->
                        </div>
                    </div>
                    <div class="equipment-category">
                        <div class="category-header">
                            <h3>护甲</h3>
                            <div class="select-buttons">
                                <button class="select-all-btn" id="select-all-armor">全选</button>
                                <button class="deselect-all-btn" id="deselect-all-armor">取消全选</button>
                            </div>
                        </div>
                        <div class="equipment-grid" id="armor-grid">
                            <!-- 护甲选项将通过JavaScript动态生成 -->
                        </div>
                    </div>
                    <div class="equipment-category">
                        <div class="category-header">
                            <h3>背包</h3>
                            <div class="select-buttons">
                                <button class="select-all-btn" id="select-all-backpacks">全选</button>
                                <button class="deselect-all-btn" id="deselect-all-backpacks">取消全选</button>
                            </div>
                        </div>
                        <div class="equipment-grid" id="backpack-grid">
                            <!-- 背包选项将通过JavaScript动态生成 -->
                        </div>
                    </div>
                    <div class="equipment-category">
                        <div class="category-header">
                            <h3>胸挂</h3>
                            <div class="select-buttons">
                                <button class="select-all-btn" id="select-all-vests">全选</button>
                                <button class="deselect-all-btn" id="deselect-all-vests">取消全选</button>
                            </div>
                        </div>
                        <div class="equipment-grid" id="vest-grid">
                            <!-- 胸挂选项将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </section>

            <!-- 枪械选择模块 -->
            <section class="selection-module" id="weapon-module">
                <div class="module-header">
                    <h2>枪械选择</h2>
                    <div class="select-buttons">
                        <button class="select-all-btn" id="select-all-weapons">全选</button>
                        <button class="deselect-all-btn" id="deselect-all-weapons">取消全选</button>
                    </div>
                </div>
                <div class="weapon-selector">
                    <h3>选择枪械</h3>

                    <!-- 手枪 -->
                    <div class="weapon-category">
                        <div class="category-header">
                            <h4>手枪</h4>
                            <div class="category-buttons">
                                <button class="select-all-btn" onclick="selectAllWeaponsByCategory('手枪')">全选</button>
                                <button class="deselect-all-btn" onclick="deselectAllWeaponsByCategory('手枪')">取消全选</button>
                            </div>
                        </div>
                        <div class="selection-grid" id="pistol-grid">
                            <!-- 手枪选项将通过JavaScript动态生成 -->
                        </div>
                    </div>

                    <!-- 冲锋枪 -->
                    <div class="weapon-category">
                        <div class="category-header">
                            <h4>冲锋枪</h4>
                            <div class="category-buttons">
                                <button class="select-all-btn" onclick="selectAllWeaponsByCategory('冲锋枪')">全选</button>
                                <button class="deselect-all-btn" onclick="deselectAllWeaponsByCategory('冲锋枪')">取消全选</button>
                            </div>
                        </div>
                        <div class="selection-grid" id="smg-grid">
                            <!-- 冲锋枪选项将通过JavaScript动态生成 -->
                        </div>
                    </div>

                    <!-- 步枪 -->
                    <div class="weapon-category">
                        <div class="category-header">
                            <h4>步枪</h4>
                            <div class="category-buttons">
                                <button class="select-all-btn" onclick="selectAllWeaponsByCategory('步枪')">全选</button>
                                <button class="deselect-all-btn" onclick="deselectAllWeaponsByCategory('步枪')">取消全选</button>
                            </div>
                        </div>
                        <div class="selection-grid" id="rifle-grid">
                            <!-- 步枪选项将通过JavaScript动态生成 -->
                        </div>
                    </div>

                    <!-- 射手步枪 -->
                    <div class="weapon-category">
                        <div class="category-header">
                            <h4>射手步枪</h4>
                            <div class="category-buttons">
                                <button class="select-all-btn" onclick="selectAllWeaponsByCategory('射手步枪')">全选</button>
                                <button class="deselect-all-btn" onclick="deselectAllWeaponsByCategory('射手步枪')">取消全选</button>
                            </div>
                        </div>
                        <div class="selection-grid" id="marksman-grid">
                            <!-- 射手步枪选项将通过JavaScript动态生成 -->
                        </div>
                    </div>

                    <!-- 狙击步枪 -->
                    <div class="weapon-category">
                        <div class="category-header">
                            <h4>狙击步枪</h4>
                            <div class="category-buttons">
                                <button class="select-all-btn" onclick="selectAllWeaponsByCategory('狙击步枪')">全选</button>
                                <button class="deselect-all-btn" onclick="deselectAllWeaponsByCategory('狙击步枪')">取消全选</button>
                            </div>
                        </div>
                        <div class="selection-grid" id="sniper-grid">
                            <!-- 狙击步枪选项将通过JavaScript动态生成 -->
                        </div>
                    </div>

                    <!-- 机枪 -->
                    <div class="weapon-category">
                        <div class="category-header">
                            <h4>机枪</h4>
                            <div class="category-buttons">
                                <button class="select-all-btn" onclick="selectAllWeaponsByCategory('机枪')">全选</button>
                                <button class="deselect-all-btn" onclick="deselectAllWeaponsByCategory('机枪')">取消全选</button>
                            </div>
                        </div>
                        <div class="selection-grid" id="lmg-grid">
                            <!-- 机枪选项将通过JavaScript动态生成 -->
                        </div>
                    </div>

                    <!-- 霰弹枪 -->
                    <div class="weapon-category">
                        <div class="category-header">
                            <h4>霰弹枪</h4>
                            <div class="category-buttons">
                                <button class="select-all-btn" onclick="selectAllWeaponsByCategory('霰弹枪')">全选</button>
                                <button class="deselect-all-btn" onclick="deselectAllWeaponsByCategory('霰弹枪')">取消全选</button>
                            </div>
                        </div>
                        <div class="selection-grid" id="shotgun-grid">
                            <!-- 霰弹枪选项将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </section>

            <!-- 玩法选择模块 -->
            <section class="selection-module">
                <div class="module-header">
                    <h2>玩法选择</h2>
                    <div class="module-buttons">
                        <button class="select-all-btn" id="select-all-gameplays">全选</button>
                        <button class="deselect-all-btn" id="deselect-all-gameplays">取消全选</button>
                    </div>
                </div>
                <div class="gameplay-selector">
                    <div class="selection-grid" id="gameplay-grid">
                        <!-- 玩法选项将通过JavaScript动态生成 -->
                    </div>
                </div>
            </section>

            <!-- 抽取按钮 -->
            <section class="draw-section">
                <button id="draw-button" class="draw-btn">开始抽取</button>
                <button id="admin-button" class="admin-btn" onclick="toggleAdminPanel()">管理物品</button>
            </section>

            <!-- 管理面板 -->
            <section class="admin-panel" id="admin-panel" style="display: none;">
                <h2>物品管理</h2>
                <div class="admin-form">
                    <div class="form-group">
                        <label for="item-category">选择分类：</label>
                        <select id="item-category" onchange="updateCategoryFields()">
                            <option value="">请选择分类</option>
                            <option value="map">地图</option>
                            <option value="operator">干员</option>
                            <option value="helmet">头盔</option>
                            <option value="armor">护甲</option>
                            <option value="backpack">背包</option>
                            <option value="vest">胸挂</option>
                            <option value="weapon">枪械</option>
                            <option value="gameplay">玩法</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="item-name">物品名称：</label>
                        <input type="text" id="item-name" placeholder="输入物品名称">
                    </div>

                    <div class="form-group">
                        <label for="item-image">上传图片：</label>
                        <input type="file" id="item-image" accept="image/*" onchange="previewImage(this)">
                        <div class="image-preview" id="image-preview" style="display: none;">
                            <img id="preview-img" src="" alt="预览图片" style="max-width: 200px; max-height: 200px; border-radius: 4px; margin-top: 10px;">
                        </div>
                    </div>

                    <!-- 装备品质选择（仅装备类显示） -->
                    <div class="form-group" id="quality-group" style="display: none;">
                        <label for="item-quality">装备品质：</label>
                        <select id="item-quality">
                            <option value="白">白色</option>
                            <option value="绿">绿色</option>
                            <option value="蓝">蓝色</option>
                            <option value="紫">紫色</option>
                            <option value="金">金色</option>
                            <option value="红">红色</option>
                        </select>
                    </div>

                    <!-- 地图难度选择（仅地图显示） -->
                    <div class="form-group" id="difficulty-group" style="display: none;">
                        <label for="item-difficulties">地图难度：</label>
                        <div class="difficulty-checkboxes">
                            <label><input type="checkbox" value="常规"> 常规</label>
                            <label><input type="checkbox" value="机密"> 机密</label>
                            <label><input type="checkbox" value="绝密"> 绝密</label>
                            <label><input type="checkbox" value="前夜"> 前夜</label>
                            <label><input type="checkbox" value="长夜"> 长夜</label>
                            <label><input type="checkbox" value="终夜"> 终夜</label>
                        </div>
                    </div>

                    <!-- 枪械类型选择（仅枪械显示） -->
                    <div class="form-group" id="weapon-type-group" style="display: none;">
                        <label for="weapon-type">枪械类型：</label>
                        <select id="weapon-type">
                            <option value="手枪">手枪</option>
                            <option value="冲锋枪">冲锋枪</option>
                            <option value="步枪">步枪</option>
                            <option value="射手步枪">射手步枪</option>
                            <option value="狙击步枪">狙击步枪</option>
                            <option value="机枪">机枪</option>
                            <option value="霰弹枪">霰弹枪</option>
                        </select>
                    </div>

                    <div class="form-actions">
                        <button type="button" onclick="addNewItem()" class="add-btn">添加物品</button>
                        <button type="button" onclick="clearSavedData()" class="clear-btn">清除所有自定义数据</button>
                        <button type="button" onclick="toggleAdminPanel()" class="cancel-btn">取消</button>
                    </div>
                </div>
            </section>

            <!-- 结果展示区域 -->
            <section class="result-section" id="result-section">
                <h2>抽取结果</h2>
                <div class="slot-machine" id="slot-machine">
                    <div class="slot-column" id="map-slot">
                        <div class="slot-header">地图</div>
                        <div class="slot-content" id="map-result">
                            <div class="slot-item">?</div>
                        </div>
                    </div>
                    <div class="slot-column" id="operator-slot">
                        <div class="slot-header">干员</div>
                        <div class="slot-content" id="operator-result">
                            <div class="slot-item">?</div>
                        </div>
                    </div>
                    <div class="slot-column" id="helmet-slot">
                        <div class="slot-header">头盔</div>
                        <div class="slot-content" id="helmet-result">
                            <div class="slot-item">?</div>
                        </div>
                    </div>
                    <div class="slot-column" id="armor-slot">
                        <div class="slot-header">护甲</div>
                        <div class="slot-content" id="armor-result">
                            <div class="slot-item">?</div>
                        </div>
                    </div>
                    <div class="slot-column" id="backpack-slot">
                        <div class="slot-header">背包</div>
                        <div class="slot-content" id="backpack-result">
                            <div class="slot-item">?</div>
                        </div>
                    </div>
                    <div class="slot-column" id="vest-slot">
                        <div class="slot-header">胸挂</div>
                        <div class="slot-content" id="vest-result">
                            <div class="slot-item">?</div>
                        </div>
                    </div>
                    <div class="slot-column" id="weapon-slot">
                        <div class="slot-header">枪械</div>
                        <div class="slot-content" id="weapon-result">
                            <div class="slot-item">?</div>
                        </div>
                    </div>
                    <div class="slot-column" id="gameplay-slot">
                        <div class="slot-header">玩法</div>
                        <div class="slot-content" id="gameplay-result">
                            <div class="slot-item">?</div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script src="data.js"></script>
    <script src="script.js"></script>
</body>
</html>
