/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON>sol<PERSON>', 'Monaco', 'Courier New', monospace;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 226, 0.2) 0%, transparent 50%),
        linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f0f23 100%);
    color: #00ff41;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        repeating-linear-gradient(
            90deg,
            transparent,
            transparent 2px,
            rgba(0, 255, 65, 0.03) 2px,
            rgba(0, 255, 65, 0.03) 4px
        ),
        repeating-linear-gradient(
            0deg,
            transparent,
            transparent 2px,
            rgba(0, 255, 65, 0.03) 2px,
            rgba(0, 255, 65, 0.03) 4px
        );
    pointer-events: none;
    z-index: 1;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    position: relative;
    z-index: 2;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 40px;
    position: relative;
}

header::before {
    content: '[ DELTA FORCE OPERATION RANDOMIZER ]';
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.8rem;
    color: #ff6b35;
    letter-spacing: 2px;
    opacity: 0.8;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow:
        0 0 10px #00ff41,
        0 0 20px #00ff41,
        0 0 30px #00ff41;
    color: #00ff41;
    font-weight: bold;
    letter-spacing: 2px;
    text-transform: uppercase;
}

header p {
    font-size: 1.1rem;
    opacity: 0.8;
    color: #8892b0;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
}

/* 选择模块样式 */
.selection-module {
    background:
        linear-gradient(135deg, rgba(15, 15, 35, 0.9) 0%, rgba(26, 26, 46, 0.9) 100%);
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 30px;
    border: 2px solid #00ff41;
    box-shadow:
        0 0 20px rgba(0, 255, 65, 0.3),
        inset 0 0 20px rgba(0, 255, 65, 0.1);
    position: relative;
}

.selection-module::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00ff41, transparent);
    animation: scanline 2s linear infinite;
}

@keyframes scanline {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.selection-module h2 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    text-align: center;
    color: #00ff41;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 0 0 10px #00ff41;
}

.selection-module h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: #ff6b35;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* 模块头部样式 */
.module-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.module-header h2 {
    margin: 0;
    text-align: left;
}

/* 装备类目头部样式 */
.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.category-header h3 {
    margin: 0;
    color: #ff6b35;
    font-size: 1.3rem;
}

/* 按钮容器样式 */
.select-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* 全选按钮样式 */
.select-all-btn {
    background: linear-gradient(135deg, #00ff41, #00cc33);
    color: #0f0f23;
    border: 2px solid #00ff41;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9em;
    font-weight: bold;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow:
        0 0 10px rgba(0, 255, 65, 0.5),
        inset 0 0 10px rgba(0, 255, 65, 0.2);
    position: relative;
    overflow: hidden;
}

.select-all-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.select-all-btn:hover::before {
    left: 100%;
}

.select-all-btn:hover {
    background: linear-gradient(135deg, #00cc33, #009926);
    transform: translateY(-2px);
    box-shadow:
        0 0 20px rgba(0, 255, 65, 0.7),
        inset 0 0 15px rgba(0, 255, 65, 0.3);
}

.select-all-btn:active {
    transform: translateY(0);
}

.select-all-btn:disabled {
    background: #333;
    color: #666;
    border-color: #666;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 取消全选按钮样式 */
.deselect-all-btn {
    background: linear-gradient(135deg, #ff3333, #cc0000);
    color: #fff;
    border: 2px solid #ff3333;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9em;
    font-weight: bold;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow:
        0 0 10px rgba(255, 51, 51, 0.5),
        inset 0 0 10px rgba(255, 51, 51, 0.2);
    position: relative;
    overflow: hidden;
}

.deselect-all-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.deselect-all-btn:hover::before {
    left: 100%;
}

.deselect-all-btn:hover {
    background: linear-gradient(135deg, #cc0000, #990000);
    transform: translateY(-2px);
    box-shadow:
        0 0 20px rgba(255, 51, 51, 0.7),
        inset 0 0 15px rgba(255, 51, 51, 0.3);
}

.deselect-all-btn:active {
    transform: translateY(0);
}

.deselect-all-btn:disabled {
    background: #333;
    color: #666;
    border-color: #666;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 网格布局 */
.selection-grid, .quality-grid, .weapon-type-grid, .equipment-type-grid, .equipment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

/* 装备分类样式 */
.equipment-categories {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.equipment-category {
    background:
        linear-gradient(135deg, rgba(15, 15, 35, 0.8) 0%, rgba(26, 26, 46, 0.8) 100%);
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #ff6b35;
    box-shadow:
        0 0 15px rgba(255, 107, 53, 0.3),
        inset 0 0 15px rgba(255, 107, 53, 0.1);
}

.equipment-category h3 {
    text-align: center;
    margin-bottom: 15px;
    color: #ff6b35;
    font-size: 1.3rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.equipment-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
}

/* 选择项样式 */
.selection-item {
    background:
        linear-gradient(135deg, rgba(15, 15, 35, 0.9) 0%, rgba(26, 26, 46, 0.9) 100%);
    border: 2px solid #333;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.selection-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 10px,
            rgba(0, 255, 65, 0.05) 10px,
            rgba(0, 255, 65, 0.05) 20px
        );
    opacity: 0;
    transition: opacity 0.3s ease;
}

.selection-item:hover::before {
    opacity: 1;
}

.selection-item:hover {
    border-color: #00ff41;
    transform: translateY(-3px);
    box-shadow:
        0 0 20px rgba(0, 255, 65, 0.5),
        0 5px 15px rgba(0, 0, 0, 0.3);
}

.selection-item.selected {
    border-color: #00ff41;
    background:
        linear-gradient(135deg, rgba(0, 255, 65, 0.2) 0%, rgba(0, 204, 51, 0.2) 100%);
    box-shadow:
        0 0 20px rgba(0, 255, 65, 0.6),
        inset 0 0 20px rgba(0, 255, 65, 0.2);
}

.selection-item img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
    margin-bottom: 10px;
    border: 1px solid #333;
    transition: all 0.3s ease;
}

.selection-item:hover img {
    border-color: #00ff41;
    box-shadow: 0 0 10px rgba(0, 255, 65, 0.5);
}

.selection-item .name {
    font-weight: bold;
    margin-bottom: 5px;
    color: #00ff41;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

.selection-item .difficulty {
    font-size: 0.8rem;
    opacity: 0.8;
    color: #8892b0;
}

.selection-item .quality {
    font-size: 0.8rem;
    font-weight: bold;
    margin-top: 5px;
    color: #ff6b35;
    text-transform: uppercase;
}

/* 装备项特殊样式 */
.equipment-item {
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background:
        linear-gradient(135deg, rgba(15, 15, 35, 0.9) 0%, rgba(26, 26, 46, 0.9) 100%);
    border: 2px solid #333;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.equipment-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 8px,
            rgba(255, 107, 53, 0.05) 8px,
            rgba(255, 107, 53, 0.05) 16px
        );
    opacity: 0;
    transition: opacity 0.3s ease;
}

.equipment-item:hover::before {
    opacity: 1;
}

.equipment-item:hover {
    border-color: #ff6b35;
    transform: translateY(-2px);
    box-shadow:
        0 0 15px rgba(255, 107, 53, 0.5),
        0 5px 10px rgba(0, 0, 0, 0.3);
}

.equipment-item.selected {
    border-color: #ff6b35;
    background:
        linear-gradient(135deg, rgba(255, 107, 53, 0.2) 0%, rgba(204, 85, 42, 0.2) 100%);
    box-shadow:
        0 0 20px rgba(255, 107, 53, 0.6),
        inset 0 0 20px rgba(255, 107, 53, 0.2);
}

.equipment-item img {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 4px;
    margin-bottom: 8px;
    border: 1px solid #333;
    transition: all 0.3s ease;
}

.equipment-item:hover img {
    border-color: #ff6b35;
    box-shadow: 0 0 8px rgba(255, 107, 53, 0.5);
}

.equipment-item .name {
    font-size: 0.9rem;
    text-align: center;
    line-height: 1.2;
    color: #00ff41;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.equipment-item .quality {
    font-size: 0.75rem;
    margin-top: 3px;
    color: #ff6b35;
    text-transform: uppercase;
}

/* 品质选择器样式 */
.quality-item {
    background:
        linear-gradient(135deg, rgba(15, 15, 35, 0.9) 0%, rgba(26, 26, 46, 0.9) 100%);
    border: 2px solid #333;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.quality-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 12px,
            rgba(0, 255, 65, 0.05) 12px,
            rgba(0, 255, 65, 0.05) 24px
        );
    opacity: 0;
    transition: opacity 0.3s ease;
}

.quality-item:hover::before {
    opacity: 1;
}

.quality-item:hover {
    border-color: #00ff41;
    transform: translateY(-3px);
    box-shadow:
        0 0 20px rgba(0, 255, 65, 0.5),
        0 5px 15px rgba(0, 0, 0, 0.3);
}

.quality-item.selected {
    border-color: #00ff41;
    background:
        linear-gradient(135deg, rgba(0, 255, 65, 0.2) 0%, rgba(0, 204, 51, 0.2) 100%);
    box-shadow:
        0 0 20px rgba(0, 255, 65, 0.6),
        inset 0 0 20px rgba(0, 255, 65, 0.2);
}

.quality-item .quality-name {
    font-weight: bold;
    font-size: 1.1rem;
    color: #00ff41;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* 抽取按钮样式 */
.draw-section {
    text-align: center;
    margin: 40px 0;
    position: relative;
}

.draw-section::before {
    content: '[ INITIATE RANDOMIZATION PROTOCOL ]';
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.8rem;
    color: #ff6b35;
    letter-spacing: 2px;
    opacity: 0.8;
}

.draw-btn {
    background:
        linear-gradient(45deg, #ff6b35, #00ff41);
    border: 3px solid #00ff41;
    border-radius: 8px;
    padding: 20px 50px;
    font-size: 1.4rem;
    font-weight: bold;
    color: #0f0f23;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 2px;
    position: relative;
    overflow: hidden;
    box-shadow:
        0 0 30px rgba(0, 255, 65, 0.6),
        0 0 60px rgba(255, 107, 53, 0.4),
        inset 0 0 30px rgba(0, 255, 65, 0.2);
}

.draw-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s;
}

.draw-btn:hover::before {
    left: 100%;
}

.draw-btn:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow:
        0 0 40px rgba(0, 255, 65, 0.8),
        0 0 80px rgba(255, 107, 53, 0.6),
        0 10px 30px rgba(0, 0, 0, 0.4);
    border-color: #ff6b35;
}

.draw-btn:active {
    transform: translateY(-2px) scale(1.02);
}

.draw-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    background: #333;
    border-color: #666;
    color: #666;
}

/* 结果展示区域 */
.result-section {
    background:
        linear-gradient(135deg, rgba(15, 15, 35, 0.95) 0%, rgba(26, 26, 46, 0.95) 100%);
    border-radius: 8px;
    padding: 25px;
    border: 2px solid #00ff41;
    box-shadow:
        0 0 30px rgba(0, 255, 65, 0.4),
        inset 0 0 30px rgba(0, 255, 65, 0.1);
    position: relative;
}

.result-section::before {
    content: '[ OPERATION RESULTS ]';
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: #0f0f23;
    padding: 0 15px;
    font-size: 0.8rem;
    color: #ff6b35;
    letter-spacing: 2px;
}

.result-section h2 {
    text-align: center;
    margin-bottom: 25px;
    font-size: 1.8rem;
    color: #00ff41;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: 0 0 15px #00ff41;
}

/* 老虎机样式 */
.slot-machine {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 15px;
    max-width: 1200px;
    margin: 0 auto;
}

.slot-column {
    background:
        linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(15, 15, 35, 0.8) 100%);
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid #333;
    transition: all 0.3s ease;
    position: relative;
}

.slot-column::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        repeating-linear-gradient(
            0deg,
            transparent,
            transparent 4px,
            rgba(0, 255, 65, 0.1) 4px,
            rgba(0, 255, 65, 0.1) 8px
        );
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.slot-column:hover::before {
    opacity: 1;
}

.slot-column:hover {
    border-color: #00ff41;
    box-shadow: 0 0 20px rgba(0, 255, 65, 0.5);
}

.slot-header {
    background:
        linear-gradient(135deg, rgba(0, 255, 65, 0.3) 0%, rgba(255, 107, 53, 0.3) 100%);
    padding: 12px;
    text-align: center;
    font-weight: bold;
    font-size: 1rem;
    color: #00ff41;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-bottom: 1px solid #333;
}

.slot-content {
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.5);
}

.slot-item {
    text-align: center;
    padding: 10px;
    font-size: 0.9rem;
    font-weight: bold;
    color: #00ff41;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.slot-item img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
    margin-bottom: 5px;
    border: 1px solid #333;
    transition: all 0.3s ease;
}

.slot-item:hover img {
    border-color: #00ff41;
    box-shadow: 0 0 15px rgba(0, 255, 65, 0.6);
}

/* 动画效果 */
@keyframes spin {
    0% { transform: translateY(0); }
    100% { transform: translateY(-300px); }
}

@keyframes slotSpin {
    0% {
        transform: translateY(0);
        filter: hue-rotate(0deg);
    }
    25% {
        transform: translateY(-30px);
        filter: hue-rotate(90deg);
    }
    50% {
        transform: translateY(-60px);
        filter: hue-rotate(180deg);
    }
    75% {
        transform: translateY(-90px);
        filter: hue-rotate(270deg);
    }
    100% {
        transform: translateY(-120px);
        filter: hue-rotate(360deg);
    }
}

@keyframes finalStop {
    0% {
        transform: translateY(-120px) scale(1.2);
        filter: brightness(1.5) saturate(1.5);
    }
    50% {
        transform: translateY(-60px) scale(1.1);
        filter: brightness(1.3) saturate(1.3);
    }
    100% {
        transform: translateY(0) scale(1);
        filter: brightness(1) saturate(1);
    }
}

@keyframes glitch {
    0%, 100% {
        transform: translateX(0);
        filter: hue-rotate(0deg);
    }
    20% {
        transform: translateX(-2px);
        filter: hue-rotate(90deg);
    }
    40% {
        transform: translateX(2px);
        filter: hue-rotate(180deg);
    }
    60% {
        transform: translateX(-1px);
        filter: hue-rotate(270deg);
    }
    80% {
        transform: translateX(1px);
        filter: hue-rotate(360deg);
    }
}

.slot-content.spinning {
    animation: slotSpin 0.1s linear infinite;
    border: 2px solid #ff6b35;
    box-shadow:
        0 0 20px rgba(255, 107, 53, 0.8),
        inset 0 0 20px rgba(255, 107, 53, 0.3);
}

.slot-content.spinning .slot-item {
    opacity: 0.7;
    filter: blur(2px) brightness(1.3);
    animation: glitch 0.1s linear infinite;
}

.slot-content.stopping {
    animation: finalStop 0.6s ease-out forwards;
    border-color: #00ff41;
    box-shadow:
        0 0 30px rgba(0, 255, 65, 0.8),
        inset 0 0 30px rgba(0, 255, 65, 0.3);
}

.slot-content.stopped .slot-item {
    opacity: 1;
    filter: none;
    transform: scale(1.05);
    transition: all 0.3s ease;
    text-shadow: 0 0 10px #00ff41;
}

.slot-content.stopped .slot-item:hover {
    transform: scale(1.1);
    text-shadow: 0 0 15px #00ff41;
}

.slot-content.stopped .slot-item img {
    box-shadow: 0 0 20px rgba(0, 255, 65, 0.6);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    header h1 {
        font-size: 2rem;
    }

    header::before {
        font-size: 0.7rem;
        top: -15px;
    }

    .selection-grid, .quality-grid, .weapon-type-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 10px;
    }

    .slot-machine {
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
    }

    .selection-item {
        padding: 10px;
    }

    .selection-item img {
        width: 50px;
        height: 50px;
    }

    .draw-btn {
        padding: 15px 30px;
        font-size: 1.2rem;
    }

    .draw-section::before {
        font-size: 0.7rem;
        top: -20px;
    }
}

@media (max-width: 480px) {
    .selection-grid, .quality-grid, .weapon-type-grid {
        grid-template-columns: 1fr;
    }

    .slot-machine {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }

    header h1 {
        font-size: 1.8rem;
    }

    .draw-btn {
        padding: 12px 25px;
        font-size: 1.1rem;
    }

    .selection-module {
        padding: 20px;
    }

    .result-section {
        padding: 20px;
    }
}

/* 额外的科技感效果 */
@keyframes dataStream {
    0% { transform: translateY(100vh); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translateY(-100vh); opacity: 0; }
}

body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        repeating-linear-gradient(
            90deg,
            transparent,
            transparent 100px,
            rgba(0, 255, 65, 0.02) 100px,
            rgba(0, 255, 65, 0.02) 101px
        );
    pointer-events: none;
    z-index: 0;
    animation: dataStream 20s linear infinite;
}
