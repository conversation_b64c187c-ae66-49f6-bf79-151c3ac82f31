// 全局变量存储用户选择
let selectedMaps = [];
let selectedOperators = [];
let selectedEquipment = [];
let selectedWeaponTypes = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeMapSelection();
    initializeOperatorSelection();
    initializeEquipmentSelection();
    initializeWeaponSelection();
    initializeDrawButton();
    initializeSelectAllButtons();
});

// 初始化地图选择模块
function initializeMapSelection() {
    const mapGrid = document.getElementById('map-grid');
    
    Object.keys(mapData).forEach(mapName => {
        const mapInfo = mapData[mapName];
        
        // 为每个地图创建选择项
        mapInfo.difficulties.forEach(difficulty => {
            const mapItem = document.createElement('div');
            mapItem.className = 'selection-item map-item';
            mapItem.dataset.map = mapName;
            mapItem.dataset.difficulty = difficulty;
            
            mapItem.innerHTML = `
                <img src="${mapInfo.image}" alt="${mapName}" onerror="this.style.display='none'">
                <div class="name">${mapName}</div>
                <div class="difficulty">${difficulty}</div>
            `;
            
            mapItem.addEventListener('click', function() {
                toggleMapSelection(this);
            });
            
            mapGrid.appendChild(mapItem);
        });
    });
}

// 切换地图选择状态
function toggleMapSelection(element) {
    const mapName = element.dataset.map;
    const difficulty = element.dataset.difficulty;
    const mapKey = `${mapName}-${difficulty}`;
    
    if (element.classList.contains('selected')) {
        element.classList.remove('selected');
        selectedMaps = selectedMaps.filter(item => item !== mapKey);
    } else {
        element.classList.add('selected');
        selectedMaps.push(mapKey);
    }
    
    console.log('选中的地图:', selectedMaps);
}

// 初始化干员选择模块
function initializeOperatorSelection() {
    const operatorGrid = document.getElementById('operator-grid');
    
    operatorData.forEach(operator => {
        const operatorItem = document.createElement('div');
        operatorItem.className = 'selection-item operator-item';
        operatorItem.dataset.operator = operator.name;
        
        operatorItem.innerHTML = `
            <img src="${operator.image}" alt="${operator.name}" onerror="this.style.display='none'">
            <div class="name">${operator.name}</div>
        `;
        
        operatorItem.addEventListener('click', function() {
            toggleOperatorSelection(this);
        });
        
        operatorGrid.appendChild(operatorItem);
    });
}

// 切换干员选择状态
function toggleOperatorSelection(element) {
    const operatorName = element.dataset.operator;
    
    if (element.classList.contains('selected')) {
        element.classList.remove('selected');
        selectedOperators = selectedOperators.filter(item => item !== operatorName);
    } else {
        element.classList.add('selected');
        selectedOperators.push(operatorName);
    }
    
    console.log('选中的干员:', selectedOperators);
}

// 初始化装备选择模块
function initializeEquipmentSelection() {
    // 初始化头盔
    initializeEquipmentCategory('helmet-grid', '头盔');

    // 初始化护甲
    initializeEquipmentCategory('armor-grid', '护甲');

    // 初始化背包
    initializeEquipmentCategory('backpack-grid', '背包');

    // 初始化胸挂
    initializeEquipmentCategory('vest-grid', '胸挂');
}

// 初始化特定装备类别
function initializeEquipmentCategory(gridId, categoryName) {
    const grid = document.getElementById(gridId);
    const equipmentList = equipmentData[categoryName];

    if (!equipmentList) return;

    equipmentList.forEach(equipment => {
        const equipmentItem = document.createElement('div');
        equipmentItem.className = 'selection-item equipment-item';
        equipmentItem.dataset.equipmentId = `${categoryName}-${equipment.name}`;

        // 根据品质设置边框颜色
        const qualityColors = {
            '白': '#ffffff',
            '绿': '#4CAF50',
            '蓝': '#2196F3',
            '紫': '#9C27B0',
            '金': '#FF9800',
            '红': '#F44336'
        };

        equipmentItem.style.borderColor = qualityColors[equipment.quality] || '#ffffff';

        equipmentItem.innerHTML = `
            <img src="${equipment.image}" alt="${equipment.name}" onerror="this.style.display='none'">
            <div class="name">${equipment.name}</div>
            <div class="quality" style="color: ${qualityColors[equipment.quality]}">${equipment.quality}色</div>
        `;

        equipmentItem.addEventListener('click', function() {
            toggleEquipmentSelection(this, equipment);
        });

        grid.appendChild(equipmentItem);
    });
}

// 切换装备选择状态
function toggleEquipmentSelection(element, equipment) {
    const equipmentId = element.dataset.equipmentId;

    if (element.classList.contains('selected')) {
        element.classList.remove('selected');
        selectedEquipment = selectedEquipment.filter(item => item.id !== equipmentId);
    } else {
        element.classList.add('selected');
        selectedEquipment.push({
            id: equipmentId,
            name: equipment.name,
            quality: equipment.quality,
            image: equipment.image,
            category: equipmentId.split('-')[0]
        });
    }

    console.log('选中的装备:', selectedEquipment);
}

// 初始化枪械选择模块
function initializeWeaponSelection() {
    const weaponTypeGrid = document.getElementById('weapon-type-grid');
    
    weaponTypes.forEach(weaponType => {
        const weaponTypeItem = document.createElement('div');
        weaponTypeItem.className = 'selection-item weapon-type-item';
        weaponTypeItem.dataset.weaponType = weaponType.name;
        
        weaponTypeItem.innerHTML = `
            <div class="name">${weaponType.name}</div>
        `;
        
        weaponTypeItem.addEventListener('click', function() {
            toggleWeaponTypeSelection(this);
        });
        
        weaponTypeGrid.appendChild(weaponTypeItem);
    });
}

// 切换枪械类型选择状态
function toggleWeaponTypeSelection(element) {
    const weaponType = element.dataset.weaponType;
    
    if (element.classList.contains('selected')) {
        element.classList.remove('selected');
        selectedWeaponTypes = selectedWeaponTypes.filter(item => item !== weaponType);
    } else {
        element.classList.add('selected');
        selectedWeaponTypes.push(weaponType);
    }
    
    console.log('选中的枪械类型:', selectedWeaponTypes);
}

// 初始化抽取按钮
function initializeDrawButton() {
    const drawButton = document.getElementById('draw-button');
    drawButton.addEventListener('click', startDraw);
}

// 开始抽取
function startDraw() {
    // 检查是否有选择
    if (selectedMaps.length === 0 && selectedOperators.length === 0 &&
        selectedEquipment.length === 0 && selectedWeaponTypes.length === 0) {
        alert('请至少选择一个类别的选项！');
        return;
    }
    
    // 禁用抽取按钮
    const drawButton = document.getElementById('draw-button');
    drawButton.disabled = true;
    drawButton.textContent = '抽取中...';
    
    // 开始老虎机动画
    startSlotMachineAnimation();
    
    // 4秒后停止动画并显示结果
    setTimeout(() => {
        stopSlotMachineAnimation();
        showResults();

        // 重新启用按钮
        drawButton.disabled = false;
        drawButton.textContent = '开始抽取';
    }, 4000);
}

// 开始老虎机动画
function startSlotMachineAnimation() {
    const slotContents = document.querySelectorAll('.slot-content');
    slotContents.forEach((content, index) => {
        content.classList.remove('stopping', 'stopped');
        content.classList.add('spinning');

        // 添加随机滚动内容
        animateSlotContent(content, index);
    });
}

// 动画化老虎机内容
function animateSlotContent(slotContent, index) {
    const slotItem = slotContent.querySelector('.slot-item');
    const randomTexts = ['?', '★', '◆', '▲', '●', '■'];

    const interval = setInterval(() => {
        const randomText = randomTexts[Math.floor(Math.random() * randomTexts.length)];
        slotItem.innerHTML = `<div style="font-size: 2rem; color: #FFD700;">${randomText}</div>`;
    }, 100);

    // 分别在不同时间停止每个老虎机，创造更真实的效果
    const stopTime = 2500 + (index * 400); // 每个老虎机延迟400ms停止
    setTimeout(() => {
        clearInterval(interval);
        stopSlotAnimation(slotContent);
    }, stopTime);
}

// 停止单个老虎机动画
function stopSlotAnimation(slotContent) {
    slotContent.classList.remove('spinning');
    slotContent.classList.add('stopping');

    setTimeout(() => {
        slotContent.classList.remove('stopping');
        slotContent.classList.add('stopped');
    }, 500);
}

// 停止老虎机动画
function stopSlotMachineAnimation() {
    const slotContents = document.querySelectorAll('.slot-content');
    slotContents.forEach(content => {
        content.classList.remove('spinning', 'stopping');
        content.classList.add('stopped');
    });
}

// 显示抽取结果
function showResults() {
    const results = {
        map: getRandomMap(),
        operator: getRandomOperator(),
        helmet: getRandomEquipmentByType('头盔'),
        armor: getRandomEquipmentByType('护甲'),
        backpack: getRandomEquipmentByType('背包'),
        vest: getRandomEquipmentByType('胸挂'),
        weapon: getRandomWeapon()
    };

    console.log('抽取结果:', results); // 调试信息
    console.log('枪械结果:', results.weapon); // 专门调试枪械

    // 延迟更新结果显示，确保动画完全停止
    setTimeout(() => {
        updateResultDisplay('map-result', results.map);
        updateResultDisplay('operator-result', results.operator);
        updateResultDisplay('helmet-result', results.helmet);
        updateResultDisplay('armor-result', results.armor);
        updateResultDisplay('backpack-result', results.backpack);
        updateResultDisplay('vest-result', results.vest);
        updateResultDisplay('weapon-result', results.weapon);

        // 额外确保枪械结果正确显示 - 使用更长的延迟
        setTimeout(() => {
            if (results.weapon) {
                console.log('二次确保枪械显示:', results.weapon);
                updateResultDisplay('weapon-result', results.weapon);
            }
        }, 200);

        // 三次确保枪械结果正确显示 - 最终保险
        setTimeout(() => {
            if (results.weapon) {
                console.log('三次确保枪械显示:', results.weapon);
                updateResultDisplay('weapon-result', results.weapon);
            }
        }, 500);
    }, 500);
}

// 更新结果显示
function updateResultDisplay(elementId, result) {
    const element = document.getElementById(elementId);
    const slotItem = element.querySelector('.slot-item');

    if (result) {
        if (result.image) {
            slotItem.innerHTML = `
                <img src="${result.image}" alt="${result.name}" onerror="this.style.display='none'">
                <div>${result.name}</div>
            `;
        } else {
            slotItem.innerHTML = `<div>${result.name || result}</div>`;
        }
    } else {
        slotItem.innerHTML = '<div>未选择</div>';
    }
}

// 随机选择地图
function getRandomMap() {
    if (selectedMaps.length === 0) return null;

    const randomIndex = Math.floor(Math.random() * selectedMaps.length);
    const selectedMapKey = selectedMaps[randomIndex];
    const [mapName, difficulty] = selectedMapKey.split('-');

    return {
        name: `${mapName} - ${difficulty}`,
        image: mapData[mapName].image
    };
}

// 随机选择干员
function getRandomOperator() {
    if (selectedOperators.length === 0) return null;

    const randomIndex = Math.floor(Math.random() * selectedOperators.length);
    const selectedOperatorName = selectedOperators[randomIndex];
    const operator = operatorData.find(op => op.name === selectedOperatorName);

    return operator;
}

// 按装备类型随机选择装备
function getRandomEquipmentByType(equipmentType) {
    // 过滤出指定类型的装备
    const typeEquipment = selectedEquipment.filter(item => item.category === equipmentType);

    if (typeEquipment.length === 0) return null;

    const randomIndex = Math.floor(Math.random() * typeEquipment.length);
    const selectedItem = typeEquipment[randomIndex];

    return {
        name: selectedItem.name,
        image: selectedItem.image,
        quality: selectedItem.quality
    };
}

// 随机选择装备（保留兼容性）
function getRandomEquipment() {
    if (selectedEquipment.length === 0) return null;

    const randomIndex = Math.floor(Math.random() * selectedEquipment.length);
    const selectedItem = selectedEquipment[randomIndex];

    return {
        name: `${selectedItem.name} (${selectedItem.category})`,
        image: selectedItem.image,
        quality: selectedItem.quality
    };
}

// 随机选择枪械
function getRandomWeapon() {
    if (selectedWeaponTypes.length === 0) return null;

    // 收集所有选中类型的枪械
    const availableWeapons = [];

    selectedWeaponTypes.forEach(weaponType => {
        const weapons = weaponData[weaponType];
        if (weapons && weapons.length > 0) {
            availableWeapons.push(...weapons);
        }
    });

    if (availableWeapons.length === 0) return null;

    const randomIndex = Math.floor(Math.random() * availableWeapons.length);
    return availableWeapons[randomIndex];
}

// 初始化全选按钮功能
function initializeSelectAllButtons() {
    // 地图全选按钮
    const selectAllMapsBtn = document.getElementById('select-all-maps');
    selectAllMapsBtn.addEventListener('click', () => selectAllItems('map'));

    // 地图取消全选按钮
    const deselectAllMapsBtn = document.getElementById('deselect-all-maps');
    deselectAllMapsBtn.addEventListener('click', () => deselectAllItems('map'));

    // 干员全选按钮
    const selectAllOperatorsBtn = document.getElementById('select-all-operators');
    selectAllOperatorsBtn.addEventListener('click', () => selectAllItems('operator'));

    // 干员取消全选按钮
    const deselectAllOperatorsBtn = document.getElementById('deselect-all-operators');
    deselectAllOperatorsBtn.addEventListener('click', () => deselectAllItems('operator'));

    // 枪械全选按钮
    const selectAllWeaponsBtn = document.getElementById('select-all-weapons');
    selectAllWeaponsBtn.addEventListener('click', () => selectAllItems('weapon'));

    // 枪械取消全选按钮
    const deselectAllWeaponsBtn = document.getElementById('deselect-all-weapons');
    deselectAllWeaponsBtn.addEventListener('click', () => deselectAllItems('weapon'));

    // 装备全选按钮
    const selectAllHelmetsBtn = document.getElementById('select-all-helmets');
    selectAllHelmetsBtn.addEventListener('click', () => selectAllItems('helmet'));

    // 头盔取消全选按钮
    const deselectAllHelmetsBtn = document.getElementById('deselect-all-helmets');
    deselectAllHelmetsBtn.addEventListener('click', () => deselectAllItems('helmet'));

    const selectAllArmorBtn = document.getElementById('select-all-armor');
    selectAllArmorBtn.addEventListener('click', () => selectAllItems('armor'));

    // 护甲取消全选按钮
    const deselectAllArmorBtn = document.getElementById('deselect-all-armor');
    deselectAllArmorBtn.addEventListener('click', () => deselectAllItems('armor'));

    const selectAllBackpacksBtn = document.getElementById('select-all-backpacks');
    selectAllBackpacksBtn.addEventListener('click', () => selectAllItems('backpack'));

    // 背包取消全选按钮
    const deselectAllBackpacksBtn = document.getElementById('deselect-all-backpacks');
    deselectAllBackpacksBtn.addEventListener('click', () => deselectAllItems('backpack'));

    const selectAllVestsBtn = document.getElementById('select-all-vests');
    selectAllVestsBtn.addEventListener('click', () => selectAllItems('vest'));

    // 胸挂取消全选按钮
    const deselectAllVestsBtn = document.getElementById('deselect-all-vests');
    deselectAllVestsBtn.addEventListener('click', () => deselectAllItems('vest'));
}

// 全选功能实现
function selectAllItems(category) {
    console.log(`全选 ${category} 类目`);

    switch(category) {
        case 'map':
            selectAllMaps();
            break;
        case 'operator':
            selectAllOperators();
            break;
        case 'weapon':
            selectAllWeapons();
            break;
        case 'helmet':
            selectAllEquipmentByType('helmet');
            break;
        case 'armor':
            selectAllEquipmentByType('armor');
            break;
        case 'backpack':
            selectAllEquipmentByType('backpack');
            break;
        case 'vest':
            selectAllEquipmentByType('vest');
            break;
    }
}

// 取消全选功能实现
function deselectAllItems(category) {
    console.log(`取消全选 ${category} 类目`);

    switch(category) {
        case 'map':
            deselectAllMaps();
            break;
        case 'operator':
            deselectAllOperators();
            break;
        case 'weapon':
            deselectAllWeapons();
            break;
        case 'helmet':
            deselectAllEquipmentByType('helmet');
            break;
        case 'armor':
            deselectAllEquipmentByType('armor');
            break;
        case 'backpack':
            deselectAllEquipmentByType('backpack');
            break;
        case 'vest':
            deselectAllEquipmentByType('vest');
            break;
    }
}

// 全选地图
function selectAllMaps() {
    const mapItems = document.querySelectorAll('.map-item');
    selectedMaps = [];

    mapItems.forEach(item => {
        item.classList.add('selected');
        const mapName = item.dataset.map;
        const difficulty = item.dataset.difficulty;
        // 使用与原有逻辑兼容的格式：mapName-difficulty
        selectedMaps.push(`${mapName}-${difficulty}`);
    });

    console.log('已选择所有地图:', selectedMaps);
}

// 全选干员
function selectAllOperators() {
    const operatorItems = document.querySelectorAll('.operator-item');
    selectedOperators = [];

    operatorItems.forEach(item => {
        item.classList.add('selected');
        const operatorName = item.dataset.operator;
        selectedOperators.push(operatorName);
    });

    console.log('已选择所有干员:', selectedOperators);
}

// 全选枪械类型
function selectAllWeapons() {
    const weaponItems = document.querySelectorAll('.weapon-type-item');
    selectedWeaponTypes = [];

    weaponItems.forEach(item => {
        item.classList.add('selected');
        const weaponType = item.dataset.weaponType;
        selectedWeaponTypes.push(weaponType);
    });

    console.log('已选择所有枪械类型:', selectedWeaponTypes);
}

// 全选指定类型的装备
function selectAllEquipmentByType(equipmentType) {
    const gridId = `${equipmentType}-grid`;
    const equipmentItems = document.querySelectorAll(`#${gridId} .equipment-item`);

    // 映射英文类型到中文类型（与抽取逻辑兼容）
    const typeMapping = {
        'helmet': '头盔',
        'armor': '护甲',
        'backpack': '背包',
        'vest': '胸挂'
    };

    const chineseType = typeMapping[equipmentType] || equipmentType;

    // 清除该类型装备的现有选择（使用正确的category字段）
    selectedEquipment = selectedEquipment.filter(item => item.category !== chineseType);

    // 获取装备数据
    const equipmentList = equipmentData[chineseType];
    if (!equipmentList) return;

    equipmentItems.forEach((item, index) => {
        item.classList.add('selected');
        const equipmentId = item.dataset.equipmentId;

        // 从装备数据中获取对应的装备信息
        const equipment = equipmentList[index];
        if (equipment) {
            // 使用与原有逻辑完全兼容的数据结构
            selectedEquipment.push({
                id: equipmentId,
                name: equipment.name,
                quality: equipment.quality,
                image: equipment.image,
                category: chineseType // 使用中文类型
            });
        }
    });

    console.log(`已选择所有${equipmentType}:`, selectedEquipment.filter(item => item.category === chineseType));
    console.log('选中的装备:', selectedEquipment);
}

// 取消全选地图
function deselectAllMaps() {
    const mapItems = document.querySelectorAll('.map-item');
    selectedMaps = [];

    mapItems.forEach(item => {
        item.classList.remove('selected');
    });

    console.log('已取消选择所有地图');
}

// 取消全选干员
function deselectAllOperators() {
    const operatorItems = document.querySelectorAll('.operator-item');
    selectedOperators = [];

    operatorItems.forEach(item => {
        item.classList.remove('selected');
    });

    console.log('已取消选择所有干员');
}

// 取消全选枪械类型
function deselectAllWeapons() {
    const weaponItems = document.querySelectorAll('.weapon-type-item');
    selectedWeaponTypes = [];

    weaponItems.forEach(item => {
        item.classList.remove('selected');
    });

    console.log('已取消选择所有枪械类型');
}

// 取消全选指定类型的装备
function deselectAllEquipmentByType(equipmentType) {
    const gridId = `${equipmentType}-grid`;
    const equipmentItems = document.querySelectorAll(`#${gridId} .equipment-item`);

    // 映射英文类型到中文类型（与抽取逻辑兼容）
    const typeMapping = {
        'helmet': '头盔',
        'armor': '护甲',
        'backpack': '背包',
        'vest': '胸挂'
    };

    const chineseType = typeMapping[equipmentType] || equipmentType;

    // 清除该类型装备的现有选择
    selectedEquipment = selectedEquipment.filter(item => item.category !== chineseType);

    equipmentItems.forEach(item => {
        item.classList.remove('selected');
    });

    console.log(`已取消选择所有${equipmentType}`);
    console.log('选中的装备:', selectedEquipment);
}
