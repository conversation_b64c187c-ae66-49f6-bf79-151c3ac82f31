// 全局变量存储用户选择
let selectedMaps = [];
let selectedOperators = [];
let selectedEquipment = [];
let selectedWeaponTypes = [];
let selectedGameplays = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 先加载保存的数据
    loadSavedData();

    initializeMapSelection();
    initializeOperatorSelection();
    initializeEquipmentSelection();
    initializeWeaponSelection();
    initializeGameplaySelection();
    initializeDrawButton();
    initializeSelectAllButtons();
});

// 数据持久化功能
function saveDataToStorage() {
    const dataToSave = {
        mapData: mapData,
        operatorData: operatorData,
        equipmentData: equipmentData,
        weaponData: weaponData,
        gameplayData: gameplayData
    };

    try {
        localStorage.setItem('deltaForceRandomizerData', JSON.stringify(dataToSave));
        console.log('数据已保存到本地存储');
    } catch (error) {
        console.error('保存数据失败:', error);
    }
}

function loadSavedData() {
    try {
        const savedData = localStorage.getItem('deltaForceRandomizerData');
        if (savedData) {
            const parsedData = JSON.parse(savedData);

            // 合并保存的数据到现有数据中
            if (parsedData.mapData) {
                Object.assign(mapData, parsedData.mapData);
            }

            if (parsedData.operatorData) {
                // 合并干员数据，避免重复
                parsedData.operatorData.forEach(newOperator => {
                    const exists = operatorData.some(existing => existing.name === newOperator.name);
                    if (!exists) {
                        operatorData.push(newOperator);
                    }
                });
            }

            if (parsedData.equipmentData) {
                Object.keys(parsedData.equipmentData).forEach(category => {
                    if (!equipmentData[category]) {
                        equipmentData[category] = [];
                    }

                    parsedData.equipmentData[category].forEach(newEquipment => {
                        const exists = equipmentData[category].some(existing => existing.name === newEquipment.name);
                        if (!exists) {
                            equipmentData[category].push(newEquipment);
                        }
                    });
                });
            }

            if (parsedData.weaponData) {
                Object.keys(parsedData.weaponData).forEach(category => {
                    if (!weaponData[category]) {
                        weaponData[category] = [];
                    }

                    parsedData.weaponData[category].forEach(newWeapon => {
                        const exists = weaponData[category].some(existing => existing.name === newWeapon.name);
                        if (!exists) {
                            weaponData[category].push(newWeapon);
                        }
                    });
                });
            }

            if (parsedData.gameplayData) {
                // 合并玩法数据，避免重复
                parsedData.gameplayData.forEach(newGameplay => {
                    const exists = gameplayData.some(existing => existing.name === newGameplay.name);
                    if (!exists) {
                        gameplayData.push(newGameplay);
                    }
                });
            }

            console.log('已加载保存的数据');
        }
    } catch (error) {
        console.error('加载保存的数据失败:', error);
    }
}

function clearSavedData() {
    if (confirm('确定要清除所有自定义添加的数据吗？这将删除您添加的所有地图、干员、装备和枪械，此操作不可撤销！')) {
        try {
            localStorage.removeItem('deltaForceRandomizerData');
            alert('自定义数据已清除！页面将刷新以重置到初始状态。');
            location.reload();
        } catch (error) {
            console.error('清除数据失败:', error);
            alert('清除数据失败，请重试。');
        }
    }
}

// 删除物品功能
function deleteItem(category, itemName, subcategory, event) {
    // 阻止事件冒泡，避免触发选择事件
    event.stopPropagation();

    if (!confirm(`确定要删除 "${itemName}" 吗？此操作不可撤销！`)) {
        return;
    }

    try {
        switch (category) {
            case 'map':
                deleteMapItem(itemName, subcategory);
                break;
            case 'operator':
                deleteOperatorItem(itemName);
                break;
            case 'equipment':
                deleteEquipmentItem(itemName, subcategory);
                break;
            case 'weapon':
                deleteWeaponItem(itemName, subcategory);
                break;
            case 'gameplay':
                deleteGameplayItem(itemName);
                break;
            default:
                alert('未知的物品类型！');
                return;
        }

        // 保存更新后的数据
        saveDataToStorage();

        // 重新加载对应的选择界面
        reloadSelectionInterface(category);

        alert('物品删除成功！');

    } catch (error) {
        console.error('删除物品时出错:', error);
        alert('删除物品失败，请重试！');
    }
}

function deleteMapItem(mapName, difficulty) {
    if (mapData[mapName]) {
        // 如果地图只有一个难度，删除整个地图
        if (mapData[mapName].difficulties.length === 1) {
            delete mapData[mapName];
        } else {
            // 否则只删除指定难度
            mapData[mapName].difficulties = mapData[mapName].difficulties.filter(d => d !== difficulty);
        }
    }
}

function deleteOperatorItem(operatorName) {
    const index = operatorData.findIndex(op => op.name === operatorName);
    if (index !== -1) {
        operatorData.splice(index, 1);
    }
}

function deleteEquipmentItem(equipmentName, category) {
    if (equipmentData[category]) {
        const index = equipmentData[category].findIndex(eq => eq.name === equipmentName);
        if (index !== -1) {
            equipmentData[category].splice(index, 1);
        }
    }
}

function deleteWeaponItem(weaponName, category) {
    if (weaponData[category]) {
        const index = weaponData[category].findIndex(wp => wp.name === weaponName);
        if (index !== -1) {
            weaponData[category].splice(index, 1);
        }
    }
}

function deleteGameplayItem(gameplayName) {
    const index = gameplayData.findIndex(gp => gp.name === gameplayName);
    if (index !== -1) {
        gameplayData.splice(index, 1);
    }
}

// 管理面板功能
function toggleAdminPanel() {
    const panel = document.getElementById('admin-panel');
    if (panel.style.display === 'none') {
        panel.style.display = 'block';
        // 重置表单
        resetAdminForm();
    } else {
        panel.style.display = 'none';
    }
}

function resetAdminForm() {
    document.getElementById('item-category').value = '';
    document.getElementById('item-name').value = '';
    document.getElementById('item-image').value = '';
    document.getElementById('item-quality').value = '白';
    document.getElementById('weapon-type').value = '手枪';

    // 重置图片预览
    document.getElementById('image-preview').style.display = 'none';
    document.getElementById('preview-img').src = '';

    // 重置难度复选框
    const checkboxes = document.querySelectorAll('.difficulty-checkboxes input[type="checkbox"]');
    checkboxes.forEach(cb => cb.checked = false);

    // 隐藏所有条件字段
    document.getElementById('quality-group').style.display = 'none';
    document.getElementById('difficulty-group').style.display = 'none';
    document.getElementById('weapon-type-group').style.display = 'none';
}

function updateCategoryFields() {
    const category = document.getElementById('item-category').value;
    const qualityGroup = document.getElementById('quality-group');
    const difficultyGroup = document.getElementById('difficulty-group');
    const weaponTypeGroup = document.getElementById('weapon-type-group');

    // 隐藏所有字段
    qualityGroup.style.display = 'none';
    difficultyGroup.style.display = 'none';
    weaponTypeGroup.style.display = 'none';

    // 根据分类显示对应字段
    if (['helmet', 'armor', 'backpack', 'vest'].includes(category)) {
        qualityGroup.style.display = 'block';
    } else if (category === 'map') {
        difficultyGroup.style.display = 'block';
    } else if (category === 'weapon') {
        weaponTypeGroup.style.display = 'block';
    }
}

// 图片预览功能
function previewImage(input) {
    const file = input.files[0];
    const preview = document.getElementById('image-preview');
    const previewImg = document.getElementById('preview-img');

    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            previewImg.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        preview.style.display = 'none';
        previewImg.src = '';
    }
}

// 将图片文件转换为Base64或保存到本地路径
function processImageFile(file, category, name) {
    return new Promise((resolve, reject) => {
        if (!file) {
            reject('没有选择图片文件');
            return;
        }

        // 创建文件路径（模拟保存到对应文件夹）
        const categoryFolders = {
            'map': '地图图片',
            'operator': '干员图片',
            'helmet': '装备/头盔',
            'armor': '装备/护甲',
            'backpack': '装备/背包',
            'vest': '装备/胸挂',
            'weapon': '枪械图片'
        };

        const folder = categoryFolders[category] || '其他图片';
        const fileName = `${name}.${file.name.split('.').pop()}`;
        const imagePath = `${folder}/${fileName}`;

        // 在实际应用中，这里应该上传文件到服务器
        // 现在我们使用Base64作为临时解决方案
        const reader = new FileReader();
        reader.onload = function(e) {
            resolve({
                path: imagePath,
                dataUrl: e.target.result
            });
        };
        reader.onerror = function() {
            reject('读取图片文件失败');
        };
        reader.readAsDataURL(file);
    });
}

function addNewItem() {
    const category = document.getElementById('item-category').value;
    const name = document.getElementById('item-name').value.trim();
    const imageFile = document.getElementById('item-image').files[0];

    if (!category || !name) {
        alert('请填写完整的物品信息！');
        return;
    }

    // 玩法不需要图片
    if (category === 'gameplay') {
        if (!name) {
            alert('请填写玩法名称！');
            return;
        }

        try {
            addNewGameplay(name);
            alert('玩法添加成功！');

            // 保存数据到本地存储
            saveDataToStorage();

            toggleAdminPanel();

            // 重新加载对应的选择界面
            reloadSelectionInterface(category);

        } catch (error) {
            console.error('添加玩法时出错:', error);
            alert('添加玩法失败，请检查输入信息！');
        }
        return;
    }

    // 其他物品需要图片
    if (!imageFile) {
        alert('请上传图片！');
        return;
    }

    // 处理图片文件
    processImageFile(imageFile, category, name)
        .then(imageInfo => {
            try {
                switch (category) {
                    case 'map':
                        addNewMap(name, imageInfo.dataUrl);
                        break;
                    case 'operator':
                        addNewOperator(name, imageInfo.dataUrl);
                        break;
                    case 'helmet':
                    case 'armor':
                    case 'backpack':
                    case 'vest':
                        addNewEquipment(category, name, imageInfo.dataUrl);
                        break;
                    case 'weapon':
                        addNewWeapon(name, imageInfo.dataUrl);
                        break;
                    case 'gameplay':
                        addNewGameplay(name);
                        break;
                    default:
                        alert('未知的分类类型！');
                        return;
                }

                alert('物品添加成功！');

                // 保存数据到本地存储
                saveDataToStorage();

                toggleAdminPanel();

                // 重新加载对应的选择界面
                reloadSelectionInterface(category);

            } catch (error) {
                console.error('添加物品时出错:', error);
                alert('添加物品失败，请检查输入信息！');
            }
        })
        .catch(error => {
            console.error('处理图片时出错:', error);
            alert('图片处理失败：' + error);
        });
}

function addNewMap(name, image) {
    // 获取选中的难度
    const checkboxes = document.querySelectorAll('.difficulty-checkboxes input[type="checkbox"]:checked');
    const difficulties = Array.from(checkboxes).map(cb => cb.value);

    if (difficulties.length === 0) {
        alert('请至少选择一个地图难度！');
        return;
    }

    // 添加到地图数据
    mapData[name] = {
        difficulties: difficulties,
        image: image
    };
}

function addNewOperator(name, image) {
    // 添加到干员数据
    operatorData.push({
        name: name,
        image: image
    });
}

function addNewEquipment(category, name, image) {
    const quality = document.getElementById('item-quality').value;

    // 获取装备类型的中文名
    const categoryMap = {
        'helmet': '头盔',
        'armor': '护甲',
        'backpack': '背包',
        'vest': '胸挂'
    };

    const categoryName = categoryMap[category];

    // 添加到装备数据
    if (!equipmentData[categoryName]) {
        equipmentData[categoryName] = [];
    }

    equipmentData[categoryName].push({
        name: name,
        quality: quality,
        image: image
    });
}

function addNewWeapon(name, image) {
    const weaponType = document.getElementById('weapon-type').value;

    // 添加到枪械数据
    if (!weaponData[weaponType]) {
        weaponData[weaponType] = [];
    }

    weaponData[weaponType].push({
        name: name,
        image: image
    });
}

function addNewGameplay(name) {
    // 获取描述（可以从表单中获取，这里先用默认值）
    const description = document.getElementById('item-name').value.trim() + '的描述';

    // 添加到玩法数据
    gameplayData.push({
        name: name,
        description: description
    });
}

function reloadSelectionInterface(category) {
    // 根据分类重新加载对应的选择界面
    switch (category) {
        case 'map':
            initializeMapSelection();
            break;
        case 'operator':
            initializeOperatorSelection();
            break;
        case 'helmet':
        case 'armor':
        case 'backpack':
        case 'vest':
        case 'equipment':
            initializeEquipmentSelection();
            break;
        case 'weapon':
            initializeWeaponSelection();
            break;
        case 'gameplay':
            initializeGameplaySelection();
            break;
    }
}

// 初始化地图选择模块
function initializeMapSelection() {
    const mapGrid = document.getElementById('map-grid');

    // 清空现有内容
    mapGrid.innerHTML = '';

    Object.keys(mapData).forEach(mapName => {
        const mapInfo = mapData[mapName];
        
        // 为每个地图创建选择项
        mapInfo.difficulties.forEach(difficulty => {
            const mapItem = document.createElement('div');
            mapItem.className = 'selection-item map-item';
            mapItem.dataset.map = mapName;
            mapItem.dataset.difficulty = difficulty;
            
            mapItem.innerHTML = `
                <div class="delete-btn" onclick="deleteItem('map', '${mapName}', '${difficulty}', event)"></div>
                <img src="${mapInfo.image}" alt="${mapName}" onerror="this.style.display='none'">
                <div class="name">${mapName}</div>
                <div class="difficulty">${difficulty}</div>
            `;
            
            mapItem.addEventListener('click', function() {
                toggleMapSelection(this);
            });
            
            mapGrid.appendChild(mapItem);
        });
    });
}

// 切换地图选择状态
function toggleMapSelection(element) {
    const mapName = element.dataset.map;
    const difficulty = element.dataset.difficulty;
    const mapKey = `${mapName}-${difficulty}`;
    
    if (element.classList.contains('selected')) {
        element.classList.remove('selected');
        selectedMaps = selectedMaps.filter(item => item !== mapKey);
    } else {
        element.classList.add('selected');
        selectedMaps.push(mapKey);
    }
    
    console.log('选中的地图:', selectedMaps);
}

// 初始化干员选择模块
function initializeOperatorSelection() {
    const operatorGrid = document.getElementById('operator-grid');

    // 清空现有内容
    operatorGrid.innerHTML = '';

    operatorData.forEach(operator => {
        const operatorItem = document.createElement('div');
        operatorItem.className = 'selection-item operator-item';
        operatorItem.dataset.operator = operator.name;
        
        operatorItem.innerHTML = `
            <div class="delete-btn" onclick="deleteItem('operator', '${operator.name}', null, event)"></div>
            <img src="${operator.image}" alt="${operator.name}" onerror="this.style.display='none'">
            <div class="name">${operator.name}</div>
        `;
        
        operatorItem.addEventListener('click', function() {
            toggleOperatorSelection(this);
        });
        
        operatorGrid.appendChild(operatorItem);
    });
}

// 切换干员选择状态
function toggleOperatorSelection(element) {
    const operatorName = element.dataset.operator;
    
    if (element.classList.contains('selected')) {
        element.classList.remove('selected');
        selectedOperators = selectedOperators.filter(item => item !== operatorName);
    } else {
        element.classList.add('selected');
        selectedOperators.push(operatorName);
    }
    
    console.log('选中的干员:', selectedOperators);
}

// 初始化装备选择模块
function initializeEquipmentSelection() {
    // 初始化头盔
    initializeEquipmentCategory('helmet-grid', '头盔');

    // 初始化护甲
    initializeEquipmentCategory('armor-grid', '护甲');

    // 初始化背包
    initializeEquipmentCategory('backpack-grid', '背包');

    // 初始化胸挂
    initializeEquipmentCategory('vest-grid', '胸挂');
}

// 初始化特定装备类别
function initializeEquipmentCategory(gridId, categoryName) {
    const grid = document.getElementById(gridId);
    const equipmentList = equipmentData[categoryName];

    if (!equipmentList) return;

    // 清空现有内容
    grid.innerHTML = '';

    equipmentList.forEach(equipment => {
        const equipmentItem = document.createElement('div');
        equipmentItem.className = 'selection-item equipment-item';
        equipmentItem.dataset.equipmentId = `${categoryName}-${equipment.name}`;

        // 根据品质设置边框颜色
        const qualityColors = {
            '白': '#ffffff',
            '绿': '#4CAF50',
            '蓝': '#2196F3',
            '紫': '#9C27B0',
            '金': '#FF9800',
            '红': '#F44336'
        };

        equipmentItem.style.borderColor = qualityColors[equipment.quality] || '#ffffff';

        equipmentItem.innerHTML = `
            <div class="delete-btn" onclick="deleteItem('equipment', '${equipment.name}', '${categoryName}', event)"></div>
            <img src="${equipment.image}" alt="${equipment.name}" onerror="this.style.display='none'">
            <div class="name">${equipment.name}</div>
            <div class="quality" style="color: ${qualityColors[equipment.quality]}">${equipment.quality}色</div>
        `;

        equipmentItem.addEventListener('click', function() {
            toggleEquipmentSelection(this, equipment);
        });

        grid.appendChild(equipmentItem);
    });
}

// 切换装备选择状态
function toggleEquipmentSelection(element, equipment) {
    const equipmentId = element.dataset.equipmentId;

    if (element.classList.contains('selected')) {
        element.classList.remove('selected');
        selectedEquipment = selectedEquipment.filter(item => item.id !== equipmentId);
    } else {
        element.classList.add('selected');
        selectedEquipment.push({
            id: equipmentId,
            name: equipment.name,
            quality: equipment.quality,
            image: equipment.image,
            category: equipmentId.split('-')[0]
        });
    }

    console.log('选中的装备:', selectedEquipment);
}

// 初始化枪械选择模块
function initializeWeaponSelection() {
    // 初始化各种枪械类型
    initializeWeaponCategory('pistol-grid', '手枪');
    initializeWeaponCategory('smg-grid', '冲锋枪');
    initializeWeaponCategory('rifle-grid', '步枪');
    initializeWeaponCategory('marksman-grid', '射手步枪');
    initializeWeaponCategory('sniper-grid', '狙击步枪');
    initializeWeaponCategory('lmg-grid', '机枪');
    initializeWeaponCategory('shotgun-grid', '霰弹枪');
}

// 初始化特定枪械类别
function initializeWeaponCategory(gridId, categoryName) {
    const grid = document.getElementById(gridId);
    const weaponList = weaponData[categoryName];

    if (!weaponList) return;

    // 清空现有内容
    grid.innerHTML = '';

    weaponList.forEach(weapon => {
        const weaponItem = document.createElement('div');
        weaponItem.className = 'selection-item weapon-item';
        weaponItem.dataset.weaponId = `${categoryName}-${weapon.name}`;

        weaponItem.innerHTML = `
            <div class="delete-btn" onclick="deleteItem('weapon', '${weapon.name}', '${categoryName}', event)"></div>
            <img src="${weapon.image}" alt="${weapon.name}" onerror="this.style.display='none'">
            <div class="name">${weapon.name}</div>
        `;

        weaponItem.addEventListener('click', function() {
            toggleWeaponSelection(this);
        });

        grid.appendChild(weaponItem);
    });
}

// 初始化玩法选择模块
function initializeGameplaySelection() {
    const gameplayGrid = document.getElementById('gameplay-grid');

    // 清空现有内容
    gameplayGrid.innerHTML = '';

    gameplayData.forEach(gameplay => {
        const gameplayItem = document.createElement('div');
        gameplayItem.className = 'selection-item gameplay-item';
        gameplayItem.dataset.gameplayName = gameplay.name;

        gameplayItem.innerHTML = `
            <div class="delete-btn" onclick="deleteItem('gameplay', '${gameplay.name}', null, event)"></div>
            <div class="gameplay-content">
                <div class="name">${gameplay.name}</div>
                <div class="description">${gameplay.description}</div>
            </div>
        `;

        gameplayItem.addEventListener('click', function() {
            toggleGameplaySelection(this);
        });

        gameplayGrid.appendChild(gameplayItem);
    });
}

// 切换玩法选择状态
function toggleGameplaySelection(element) {
    const gameplayName = element.dataset.gameplayName;

    if (element.classList.contains('selected')) {
        element.classList.remove('selected');
        selectedGameplays = selectedGameplays.filter(item => item !== gameplayName);
    } else {
        element.classList.add('selected');
        selectedGameplays.push(gameplayName);
    }

    console.log('选中的玩法:', selectedGameplays);
}

// 切换枪械选择状态
function toggleWeaponSelection(element) {
    const weaponId = element.dataset.weaponId;

    if (element.classList.contains('selected')) {
        element.classList.remove('selected');
        selectedWeaponTypes = selectedWeaponTypes.filter(item => item !== weaponId);
    } else {
        element.classList.add('selected');
        selectedWeaponTypes.push(weaponId);
    }

    console.log('选中的枪械:', selectedWeaponTypes);
}

// 初始化抽取按钮
function initializeDrawButton() {
    const drawButton = document.getElementById('draw-button');
    drawButton.addEventListener('click', startDraw);
}

// 开始抽取
function startDraw() {
    // 检查是否有选择
    if (selectedMaps.length === 0 && selectedOperators.length === 0 &&
        selectedEquipment.length === 0 && selectedWeaponTypes.length === 0 &&
        selectedGameplays.length === 0) {
        alert('请至少选择一个类别的选项！');
        return;
    }
    
    // 禁用抽取按钮
    const drawButton = document.getElementById('draw-button');
    drawButton.disabled = true;
    drawButton.textContent = '抽取中...';
    
    // 开始老虎机动画
    startSlotMachineAnimation();
    
    // 4秒后停止动画并显示结果
    setTimeout(() => {
        stopSlotMachineAnimation();
        showResults();

        // 重新启用按钮
        drawButton.disabled = false;
        drawButton.textContent = '开始抽取';
    }, 4000);
}

// 开始老虎机动画
function startSlotMachineAnimation() {
    const slotContents = document.querySelectorAll('.slot-content');
    slotContents.forEach((content, index) => {
        content.classList.remove('stopping', 'stopped');
        content.classList.add('spinning');

        // 添加随机滚动内容
        animateSlotContent(content, index);
    });
}

// 动画化老虎机内容
function animateSlotContent(slotContent, index) {
    const slotItem = slotContent.querySelector('.slot-item');
    const randomTexts = ['?']; // 只使用问号

    const interval = setInterval(() => {
        const randomText = randomTexts[Math.floor(Math.random() * randomTexts.length)];
        slotItem.innerHTML = `<div style="font-size: 2rem; color: #FFD700; animation: rotateQuestion 0.5s linear infinite;">${randomText}</div>`;
    }, 100);

    // 分别在不同时间停止每个老虎机，创造更真实的效果
    const stopTime = 2500 + (index * 400); // 每个老虎机延迟400ms停止
    setTimeout(() => {
        clearInterval(interval);
        stopSlotAnimation(slotContent);
    }, stopTime);
}

// 停止单个老虎机动画
function stopSlotAnimation(slotContent) {
    slotContent.classList.remove('spinning');
    slotContent.classList.add('stopping');

    setTimeout(() => {
        slotContent.classList.remove('stopping');
        slotContent.classList.add('stopped');
    }, 500);
}

// 停止老虎机动画
function stopSlotMachineAnimation() {
    const slotContents = document.querySelectorAll('.slot-content');
    slotContents.forEach(content => {
        content.classList.remove('spinning', 'stopping');
        content.classList.add('stopped');
    });
}

// 显示抽取结果
function showResults() {
    const results = {
        map: getRandomMap(),
        operator: getRandomOperator(),
        helmet: getRandomEquipmentByType('头盔'),
        armor: getRandomEquipmentByType('护甲'),
        backpack: getRandomEquipmentByType('背包'),
        vest: getRandomEquipmentByType('胸挂'),
        weapon: getRandomWeapon(),
        gameplay: getRandomGameplay()
    };

    console.log('抽取结果:', results); // 调试信息
    console.log('枪械结果:', results.weapon); // 专门调试枪械

    // 延迟更新结果显示，确保动画完全停止
    setTimeout(() => {
        updateResultDisplay('map-result', results.map);
        updateResultDisplay('operator-result', results.operator);
        updateResultDisplay('helmet-result', results.helmet);
        updateResultDisplay('armor-result', results.armor);
        updateResultDisplay('backpack-result', results.backpack);
        updateResultDisplay('vest-result', results.vest);
        updateResultDisplay('weapon-result', results.weapon);
        updateResultDisplay('gameplay-result', results.gameplay);

        // 额外确保枪械结果正确显示 - 使用更长的延迟
        setTimeout(() => {
            if (results.weapon) {
                console.log('二次确保枪械显示:', results.weapon);
                updateResultDisplay('weapon-result', results.weapon);
            }
        }, 200);

        // 三次确保枪械结果正确显示 - 最终保险
        setTimeout(() => {
            if (results.weapon) {
                console.log('三次确保枪械显示:', results.weapon);
                updateResultDisplay('weapon-result', results.weapon);
            }
        }, 500);
    }, 500);
}

// 更新结果显示
function updateResultDisplay(elementId, result) {
    const element = document.getElementById(elementId);
    const slotItem = element.querySelector('.slot-item');

    if (result) {
        if (result.image) {
            slotItem.innerHTML = `
                <img src="${result.image}" alt="${result.name}" onerror="this.style.display='none'">
                <div>${result.name}</div>
            `;
        } else if (result.description) {
            // 玩法显示
            slotItem.innerHTML = `
                <div class="gameplay-result">
                    <div class="gameplay-name">${result.name}</div>
                    <div class="gameplay-desc">${result.description}</div>
                </div>
            `;
        } else {
            slotItem.innerHTML = `<div>${result.name || result}</div>`;
        }
    } else {
        slotItem.innerHTML = '<div>未选择</div>';
    }
}

// 随机选择地图
function getRandomMap() {
    if (selectedMaps.length === 0) return null;

    const randomIndex = Math.floor(Math.random() * selectedMaps.length);
    const selectedMapKey = selectedMaps[randomIndex];
    const [mapName, difficulty] = selectedMapKey.split('-');

    return {
        name: `${mapName} - ${difficulty}`,
        image: mapData[mapName].image
    };
}

// 随机选择干员
function getRandomOperator() {
    if (selectedOperators.length === 0) return null;

    const randomIndex = Math.floor(Math.random() * selectedOperators.length);
    const selectedOperatorName = selectedOperators[randomIndex];
    const operator = operatorData.find(op => op.name === selectedOperatorName);

    return operator;
}

// 按装备类型随机选择装备
function getRandomEquipmentByType(equipmentType) {
    // 过滤出指定类型的装备
    const typeEquipment = selectedEquipment.filter(item => item.category === equipmentType);

    if (typeEquipment.length === 0) return null;

    const randomIndex = Math.floor(Math.random() * typeEquipment.length);
    const selectedItem = typeEquipment[randomIndex];

    return {
        name: selectedItem.name,
        image: selectedItem.image,
        quality: selectedItem.quality
    };
}

// 随机选择装备（保留兼容性）
function getRandomEquipment() {
    if (selectedEquipment.length === 0) return null;

    const randomIndex = Math.floor(Math.random() * selectedEquipment.length);
    const selectedItem = selectedEquipment[randomIndex];

    return {
        name: `${selectedItem.name} (${selectedItem.category})`,
        image: selectedItem.image,
        quality: selectedItem.quality
    };
}

// 随机选择枪械
function getRandomWeapon() {
    if (selectedWeaponTypes.length === 0) return null;

    // 收集所有选中的具体枪械
    const availableWeapons = [];

    selectedWeaponTypes.forEach(weaponId => {
        // weaponId 格式为 "类型-枪械名称"，例如 "手枪-G17"
        const [weaponType, weaponName] = weaponId.split('-');
        const weapons = weaponData[weaponType];

        if (weapons && weapons.length > 0) {
            const weapon = weapons.find(w => w.name === weaponName);
            if (weapon) {
                availableWeapons.push(weapon);
            }
        }
    });

    if (availableWeapons.length === 0) return null;

    const randomIndex = Math.floor(Math.random() * availableWeapons.length);
    return availableWeapons[randomIndex];
}

// 随机选择玩法
function getRandomGameplay() {
    if (selectedGameplays.length === 0) return null;

    // 收集所有选中的玩法
    const availableGameplays = [];

    selectedGameplays.forEach(gameplayName => {
        const gameplay = gameplayData.find(gp => gp.name === gameplayName);
        if (gameplay) {
            availableGameplays.push(gameplay);
        }
    });

    if (availableGameplays.length === 0) return null;

    const randomIndex = Math.floor(Math.random() * availableGameplays.length);
    return availableGameplays[randomIndex];
}

// 初始化全选按钮功能
function initializeSelectAllButtons() {
    // 地图全选按钮
    const selectAllMapsBtn = document.getElementById('select-all-maps');
    selectAllMapsBtn.addEventListener('click', () => selectAllItems('map'));

    // 地图取消全选按钮
    const deselectAllMapsBtn = document.getElementById('deselect-all-maps');
    deselectAllMapsBtn.addEventListener('click', () => deselectAllItems('map'));

    // 干员全选按钮
    const selectAllOperatorsBtn = document.getElementById('select-all-operators');
    selectAllOperatorsBtn.addEventListener('click', () => selectAllItems('operator'));

    // 干员取消全选按钮
    const deselectAllOperatorsBtn = document.getElementById('deselect-all-operators');
    deselectAllOperatorsBtn.addEventListener('click', () => deselectAllItems('operator'));

    // 枪械全选按钮
    const selectAllWeaponsBtn = document.getElementById('select-all-weapons');
    selectAllWeaponsBtn.addEventListener('click', () => selectAllItems('weapon'));

    // 枪械取消全选按钮
    const deselectAllWeaponsBtn = document.getElementById('deselect-all-weapons');
    deselectAllWeaponsBtn.addEventListener('click', () => deselectAllItems('weapon'));

    // 玩法全选按钮
    const selectAllGameplaysBtn = document.getElementById('select-all-gameplays');
    selectAllGameplaysBtn.addEventListener('click', () => selectAllItems('gameplay'));

    // 玩法取消全选按钮
    const deselectAllGameplaysBtn = document.getElementById('deselect-all-gameplays');
    deselectAllGameplaysBtn.addEventListener('click', () => deselectAllItems('gameplay'));

    // 装备全选按钮
    const selectAllHelmetsBtn = document.getElementById('select-all-helmets');
    selectAllHelmetsBtn.addEventListener('click', () => selectAllItems('helmet'));

    // 头盔取消全选按钮
    const deselectAllHelmetsBtn = document.getElementById('deselect-all-helmets');
    deselectAllHelmetsBtn.addEventListener('click', () => deselectAllItems('helmet'));

    const selectAllArmorBtn = document.getElementById('select-all-armor');
    selectAllArmorBtn.addEventListener('click', () => selectAllItems('armor'));

    // 护甲取消全选按钮
    const deselectAllArmorBtn = document.getElementById('deselect-all-armor');
    deselectAllArmorBtn.addEventListener('click', () => deselectAllItems('armor'));

    const selectAllBackpacksBtn = document.getElementById('select-all-backpacks');
    selectAllBackpacksBtn.addEventListener('click', () => selectAllItems('backpack'));

    // 背包取消全选按钮
    const deselectAllBackpacksBtn = document.getElementById('deselect-all-backpacks');
    deselectAllBackpacksBtn.addEventListener('click', () => deselectAllItems('backpack'));

    const selectAllVestsBtn = document.getElementById('select-all-vests');
    selectAllVestsBtn.addEventListener('click', () => selectAllItems('vest'));

    // 胸挂取消全选按钮
    const deselectAllVestsBtn = document.getElementById('deselect-all-vests');
    deselectAllVestsBtn.addEventListener('click', () => deselectAllItems('vest'));
}

// 全选功能实现
function selectAllItems(category) {
    console.log(`全选 ${category} 类目`);

    switch(category) {
        case 'map':
            selectAllMaps();
            break;
        case 'operator':
            selectAllOperators();
            break;
        case 'weapon':
            selectAllWeapons();
            break;
        case 'helmet':
            selectAllEquipmentByType('helmet');
            break;
        case 'armor':
            selectAllEquipmentByType('armor');
            break;
        case 'backpack':
            selectAllEquipmentByType('backpack');
            break;
        case 'vest':
            selectAllEquipmentByType('vest');
            break;
        case 'gameplay':
            selectAllGameplays();
            break;
    }
}

// 取消全选功能实现
function deselectAllItems(category) {
    console.log(`取消全选 ${category} 类目`);

    switch(category) {
        case 'map':
            deselectAllMaps();
            break;
        case 'operator':
            deselectAllOperators();
            break;
        case 'weapon':
            deselectAllWeapons();
            break;
        case 'helmet':
            deselectAllEquipmentByType('helmet');
            break;
        case 'armor':
            deselectAllEquipmentByType('armor');
            break;
        case 'backpack':
            deselectAllEquipmentByType('backpack');
            break;
        case 'vest':
            deselectAllEquipmentByType('vest');
            break;
    }
}

// 全选地图
function selectAllMaps() {
    const mapItems = document.querySelectorAll('.map-item');
    selectedMaps = [];

    mapItems.forEach(item => {
        item.classList.add('selected');
        const mapName = item.dataset.map;
        const difficulty = item.dataset.difficulty;
        // 使用与原有逻辑兼容的格式：mapName-difficulty
        selectedMaps.push(`${mapName}-${difficulty}`);
    });

    console.log('已选择所有地图:', selectedMaps);
}

// 全选干员
function selectAllOperators() {
    const operatorItems = document.querySelectorAll('.operator-item');
    selectedOperators = [];

    operatorItems.forEach(item => {
        item.classList.add('selected');
        const operatorName = item.dataset.operator;
        selectedOperators.push(operatorName);
    });

    console.log('已选择所有干员:', selectedOperators);
}

// 全选枪械
function selectAllWeapons() {
    const weaponItems = document.querySelectorAll('.weapon-item');
    selectedWeaponTypes = [];

    weaponItems.forEach(item => {
        item.classList.add('selected');
        const weaponId = item.dataset.weaponId;
        selectedWeaponTypes.push(weaponId);
    });

    console.log('已选择所有枪械:', selectedWeaponTypes);
}

// 全选指定类型的装备
function selectAllEquipmentByType(equipmentType) {
    const gridId = `${equipmentType}-grid`;
    const equipmentItems = document.querySelectorAll(`#${gridId} .equipment-item`);

    // 映射英文类型到中文类型（与抽取逻辑兼容）
    const typeMapping = {
        'helmet': '头盔',
        'armor': '护甲',
        'backpack': '背包',
        'vest': '胸挂'
    };

    const chineseType = typeMapping[equipmentType] || equipmentType;

    // 清除该类型装备的现有选择（使用正确的category字段）
    selectedEquipment = selectedEquipment.filter(item => item.category !== chineseType);

    // 获取装备数据
    const equipmentList = equipmentData[chineseType];
    if (!equipmentList) return;

    equipmentItems.forEach((item, index) => {
        item.classList.add('selected');
        const equipmentId = item.dataset.equipmentId;

        // 从装备数据中获取对应的装备信息
        const equipment = equipmentList[index];
        if (equipment) {
            // 使用与原有逻辑完全兼容的数据结构
            selectedEquipment.push({
                id: equipmentId,
                name: equipment.name,
                quality: equipment.quality,
                image: equipment.image,
                category: chineseType // 使用中文类型
            });
        }
    });

    console.log(`已选择所有${equipmentType}:`, selectedEquipment.filter(item => item.category === chineseType));
    console.log('选中的装备:', selectedEquipment);
}

// 取消全选地图
function deselectAllMaps() {
    const mapItems = document.querySelectorAll('.map-item');
    selectedMaps = [];

    mapItems.forEach(item => {
        item.classList.remove('selected');
    });

    console.log('已取消选择所有地图');
}

// 取消全选干员
function deselectAllOperators() {
    const operatorItems = document.querySelectorAll('.operator-item');
    selectedOperators = [];

    operatorItems.forEach(item => {
        item.classList.remove('selected');
    });

    console.log('已取消选择所有干员');
}

// 取消全选枪械
function deselectAllWeapons() {
    const weaponItems = document.querySelectorAll('.weapon-item');
    selectedWeaponTypes = [];

    weaponItems.forEach(item => {
        item.classList.remove('selected');
    });

    console.log('已取消选择所有枪械');
}

// 按类别全选枪械
function selectAllWeaponsByCategory(category) {
    const categoryMap = {
        '手枪': 'pistol-grid',
        '冲锋枪': 'smg-grid',
        '步枪': 'rifle-grid',
        '射手步枪': 'marksman-grid',
        '狙击步枪': 'sniper-grid',
        '机枪': 'lmg-grid',
        '霰弹枪': 'shotgun-grid'
    };

    const gridId = categoryMap[category];
    if (!gridId) return;

    const weaponItems = document.querySelectorAll(`#${gridId} .weapon-item`);

    weaponItems.forEach(item => {
        if (!item.classList.contains('selected')) {
            item.classList.add('selected');
            const weaponId = item.dataset.weaponId;
            if (!selectedWeaponTypes.includes(weaponId)) {
                selectedWeaponTypes.push(weaponId);
            }
        }
    });

    console.log(`已选择所有${category}:`, selectedWeaponTypes);
}

// 按类别取消全选枪械
function deselectAllWeaponsByCategory(category) {
    const categoryMap = {
        '手枪': 'pistol-grid',
        '冲锋枪': 'smg-grid',
        '步枪': 'rifle-grid',
        '射手步枪': 'marksman-grid',
        '狙击步枪': 'sniper-grid',
        '机枪': 'lmg-grid',
        '霰弹枪': 'shotgun-grid'
    };

    const gridId = categoryMap[category];
    if (!gridId) return;

    const weaponItems = document.querySelectorAll(`#${gridId} .weapon-item`);

    weaponItems.forEach(item => {
        if (item.classList.contains('selected')) {
            item.classList.remove('selected');
            const weaponId = item.dataset.weaponId;
            selectedWeaponTypes = selectedWeaponTypes.filter(id => id !== weaponId);
        }
    });

    console.log(`已取消选择所有${category}:`, selectedWeaponTypes);
}

// 取消全选指定类型的装备
function deselectAllEquipmentByType(equipmentType) {
    const gridId = `${equipmentType}-grid`;
    const equipmentItems = document.querySelectorAll(`#${gridId} .equipment-item`);

    // 映射英文类型到中文类型（与抽取逻辑兼容）
    const typeMapping = {
        'helmet': '头盔',
        'armor': '护甲',
        'backpack': '背包',
        'vest': '胸挂'
    };

    const chineseType = typeMapping[equipmentType] || equipmentType;

    // 清除该类型装备的现有选择
    selectedEquipment = selectedEquipment.filter(item => item.category !== chineseType);

    equipmentItems.forEach(item => {
        item.classList.remove('selected');
    });

    console.log(`已取消选择所有${equipmentType}`);
    console.log('选中的装备:', selectedEquipment);
}
